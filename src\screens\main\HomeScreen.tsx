import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  FlatList,
  ActivityIndicator,
  Pressable,
  RefreshControl,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchVideos, fetchCategories, fetchRecommendations } from '../../store/slices/videoSlice';
import type { Video, Category } from '../../types';
import { GoGoColors } from '../../../constants/Colors';
import { Ionicons } from '@expo/vector-icons';
// Temporarily comment out to avoid import issues
// import DatabaseTestComponent from '../../components/DatabaseTestComponent';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming,
  interpolate,
  Extrapolate,
  FadeIn,
  SlideInRight,
} from 'react-native-reanimated';
import { useResponsiveLayout } from '../../utils/responsive';
import { hapticFeedback } from '../../utils/animations';
import { formatViewCount, formatDuration } from '../../utils/formatters';

const { width, height } = Dimensions.get('window');

// Modern Video Card Component
interface ModernVideoCardProps {
  video: Video;
  onPress: (video: Video) => void;
  index: number;
  style?: 'featured' | 'grid' | 'list';
}

function ModernVideoCard({ video, onPress, index, style = 'grid' }: ModernVideoCardProps) {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(0);

  useEffect(() => {
    opacity.value = withTiming(1, { duration: 300 + index * 100 });
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
    hapticFeedback.light();
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  // All videos are premium
  const isPPV = typeof video.price === 'number' && video.price > 0;
  const hasAccess = true; // All videos are premium, so access logic is unified

  // Always show premium badge
  const PremiumBadge = (
    <View style={style === 'featured' ? styles.premiumBadge : styles.cardPremiumBadge}>
      <Ionicons name="diamond" size={style === 'featured' ? 12 : 10} color="#FFFFFF" />
      {style === 'featured' && <Text style={styles.premiumText}>PREMIUM</Text>}
    </View>
  );

  // PPV Badge
  const PPVBadge = isPPV ? (
    <View style={{
      position: 'absolute',
      top: 8,
      left: 8,
      backgroundColor: GoGoColors.highlightGold,
      borderRadius: 8,
      paddingHorizontal: 8,
      paddingVertical: 2,
      flexDirection: 'row',
      alignItems: 'center',
      zIndex: 10,
    }}>
      <Ionicons name="cash-outline" size={12} color="#fff" style={{ marginRight: 4 }} />
      <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 12 }}>PPV • MWK {video.price}</Text>
    </View>
  ) : null;

  if (style === 'featured') {
    return (
      <Animated.View style={[styles.featuredCard, animatedStyle]} entering={FadeIn.delay(200)}>
        <TouchableOpacity
          onPress={() => onPress(video)}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={1}
        >
          <Image source={{ uri: video.thumbnail_url }} style={styles.featuredImage} />
          {PPVBadge}
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)']}
            style={styles.featuredGradient}
          >
            <View style={styles.featuredContent}>
              <View style={styles.featuredBadges}>
                {PremiumBadge}
                <View style={styles.categoryBadge}>
                  <Text style={styles.categoryText}>{video.category?.name || 'General'}</Text>
                </View>
              </View>
              <Text style={styles.featuredTitle} numberOfLines={2}>{video.title}</Text>
              <Text style={styles.featuredCreator}>by {video.creator.username}</Text>
              <View style={styles.featuredStats}>
                <View style={styles.statItem}>
                  <Ionicons name="eye" size={14} color={GoGoColors.textMuted} />
                  <Text style={styles.statText}>{formatViewCount(video.view_count)}</Text>
                </View>
                <View style={styles.statItem}>
                  <Ionicons name="time" size={14} color={GoGoColors.textMuted} />
                  <Text style={styles.statText}>{formatDuration(video.duration)}</Text>
                </View>
              </View>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[styles.modernCard, animatedStyle]} entering={SlideInRight.delay(index * 100)}>
      <TouchableOpacity
        onPress={() => onPress(video)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
        style={styles.cardTouchable}
      >
        <View style={styles.cardImageContainer}>
          <Image source={{ uri: video.thumbnail_url }} style={styles.cardImage} />
          {PPVBadge}
          {PremiumBadge}
          <View style={styles.playOverlay}>
            <Ionicons name="play" size={20} color="#FFFFFF" />
          </View>
        </View>
        <View style={styles.cardContent}>
          <Text style={styles.cardTitle} numberOfLines={2}>{video.title}</Text>
          <Text style={styles.cardCreator} numberOfLines={1}>{video.creator.username}</Text>
          <View style={styles.cardStats}>
            <Text style={styles.cardStatText}>{formatViewCount(video.view_count)} views</Text>
            <Text style={styles.cardDuration}>{formatDuration(video.duration)}</Text>
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

// Modern Section Header Component
interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  onSeeAll?: () => void;
  icon?: string;
}

function SectionHeader({ title, subtitle, onSeeAll, icon }: SectionHeaderProps) {
  return (
    <View style={styles.sectionHeader}>
      <View style={styles.sectionTitleContainer}>
        {icon && <Ionicons name={icon as any} size={24} color={GoGoColors.primary} />}
        <View style={styles.sectionTextContainer}>
          <Text style={styles.sectionTitle}>{title}</Text>
          {subtitle && <Text style={styles.sectionSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {onSeeAll && (
        <TouchableOpacity onPress={onSeeAll} style={styles.seeAllButton}>
          <Text style={styles.seeAllText}>See All</Text>
          <Ionicons name="chevron-forward" size={16} color={GoGoColors.primary} />
        </TouchableOpacity>
      )}
    </View>
  );
}

// Category Filter Component
interface CategoryFilterProps {
  categories: Category[];
  selectedCategory: string | null;
  onSelectCategory: (categoryId: string | null) => void;
}

function CategoryFilter({ categories, selectedCategory, onSelectCategory }: CategoryFilterProps) {
  return (
    <View style={styles.categoryFilter}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.categoryScroll}>
        <TouchableOpacity
          style={[styles.categoryChip, !selectedCategory && styles.categoryChipActive]}
          onPress={() => onSelectCategory(null)}
        >
          <Text style={[styles.categoryChipText, !selectedCategory && styles.categoryChipTextActive]}>
            All
          </Text>
        </TouchableOpacity>
        {categories.map((category: Category) => (
          <TouchableOpacity
            key={category.id}
            style={[styles.categoryChip, selectedCategory === category.id && styles.categoryChipActive]}
            onPress={() => onSelectCategory(category.id)}
          >
            <Text style={[styles.categoryChipText, selectedCategory === category.id && styles.categoryChipTextActive]}>
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

interface Props {
  onVideoPress: (video: Video) => void;
}

export default function HomeScreen({ onVideoPress }: Props) {
  const dispatch = useAppDispatch();
  const { videos, categories, recommendations, isLoading } = useAppSelector((state) => state.video);
  const { user } = useAppSelector((state) => state.auth);
  const { watchLater, viewingHistory } = useAppSelector((state) => state.userPreferences);
  const layout = useResponsiveLayout();

  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [notifications, setNotifications] = useState([
    { id: '1', text: 'Creator Jane uploaded a new video!', read: false, timestamp: '2m ago' },
    { id: '2', text: 'Creator Mike uploaded a new video!', read: true, timestamp: '1h ago' },
  ]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showDatabaseTest, setShowDatabaseTest] = useState(false);
  const unreadCount = notifications.filter(n => !n.read).length;

  useEffect(() => {
    loadData();
  }, [dispatch, user]);

  const loadData = async () => {
    console.log('Loading data...');
    const results = await Promise.all([
      dispatch(fetchVideos({ page: 1, limit: 50 })),
      dispatch(fetchCategories()),
      user && dispatch(fetchRecommendations(user.id))
    ]);
    console.log('Data loaded:', results);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const getFilteredVideos = () => {
    let filtered = videos;

    if (selectedCategory) {
      filtered = filtered.filter(video => video.category_id === selectedCategory);
    }

    if (searchQuery) {
      filtered = filtered.filter(video =>
        video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        video.creator.username.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  };

  const getFeaturedVideos = () => {
    return [...videos].sort((a, b) => b.view_count - a.view_count).slice(0, 3);
  };

  const getTrendingVideos = () => {
    return [...videos].sort((a, b) => b.view_count - a.view_count).slice(3, 13);
  };

  const getRecentVideos = () => {
    return [...videos].sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    ).slice(0, 10);
  };

  const renderGridVideos = (videosToRender: Video[]) => {
    const numColumns = layout.isTablet ? 3 : 2;

    return (
      <FlatList
        data={videosToRender}
        numColumns={numColumns}
        keyExtractor={(item) => item.id}
        renderItem={({ item, index }: { item: Video; index: number }) => (
          <ModernVideoCard
            video={item}
            onPress={onVideoPress}
            index={index}
            style="grid"
          />
        )}
        contentContainerStyle={styles.gridContainer}
        scrollEnabled={false}
      />
    );
  };

  // Watch Later row
  const renderWatchLaterRow = () => (
    watchLater && watchLater.length > 0 && (
      <View style={styles.section}>
        <SectionHeader
          title="Watch Later"
          subtitle="Saved for later viewing"
          icon="time"
        />
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.featuredScroll}
        >
          {watchLater.map((video: Video, index: number) => (
            <ModernVideoCard
              key={video.id}
              video={video}
              onPress={onVideoPress}
              index={index}
              style="featured"
            />
          ))}
        </ScrollView>
      </View>
    )
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={GoGoColors.primary} />
        <Text style={styles.loadingText}>Loading amazing content...</Text>
      </View>
    );
  }

  const featuredVideos = getFeaturedVideos();
  const trendingVideos = getTrendingVideos();
  const recentVideos = getRecentVideos();
  const filteredVideos = getFilteredVideos();

  // TODO: Implement real personalized recommendations from database
  const becauseYouWatched = videos.slice(0, 6);
  const fromCreatorsYouFollow = videos.slice(6, 12);
  const forYou = recommendations.slice(0, 6);
  const hotCategories = categories.filter((c, i) => i % 3 === 0);
  const newCategories = categories.filter((c, i) => i % 3 === 1);
  const risingCategories = categories.filter((c, i) => i % 3 === 2);

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[GoGoColors.primary]}
          tintColor={GoGoColors.primary}
          progressViewOffset={-20}
        />
      }
    >
      {/* Modern Header */}
      <View style={styles.header}>
        <LinearGradient
          colors={GoGoColors.backgroundGradient}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerTop}>
              <Text style={styles.welcomeText}>
                Welcome back{user ? `, ${user.full_name?.split(' ')[0]}` : ''}! 👋
              </Text>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TouchableOpacity
                  style={[styles.notificationButton, { marginRight: 10 }]}
                  onPress={() => setShowDatabaseTest(true)}
                >
                  <Ionicons name="server-outline" size={24} color={GoGoColors.textPrimary} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.notificationButton} onPress={() => setShowNotifications(true)}>
                  <Ionicons name="notifications-outline" size={24} color={GoGoColors.textPrimary} />
                  {unreadCount > 0 && <View style={styles.notificationBadge} />}
                </TouchableOpacity>
              </View>
            </View>
            <Text style={styles.headerTitle}>Discover Amazing Content</Text>
          </View>
        </LinearGradient>
      </View>

      {/* Category Filter */}
      <CategoryFilter
        categories={categories}
        selectedCategory={selectedCategory}
        onSelectCategory={setSelectedCategory}
      />

      {/* Featured Videos Carousel */}
      {featuredVideos.length > 0 && (
        <View style={styles.featuredSection}>
          <SectionHeader
            title="Featured Today"
            subtitle="Hand-picked content just for you"
            icon="star"
          />
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.featuredScroll}
            decelerationRate="fast"
            snapToInterval={width * 0.85 + 16}
            snapToAlignment="start"
          >
            {featuredVideos.map((video, index) => (
              <ModernVideoCard
                key={video.id}
                video={video}
                onPress={onVideoPress}
                index={index}
                style="featured"
              />
            ))}
          </ScrollView>
        </View>
      )}

      {/* Watch Later */}
      {renderWatchLaterRow()}

      {/* Trending Section */}
      {trendingVideos.length > 0 && (
        <View style={styles.section}>
          <SectionHeader
            title="Trending Now"
            subtitle="What everyone's watching"
            icon="trending-up"
            onSeeAll={() => {/* Navigate to trending page */}}
          />
          {renderGridVideos(trendingVideos)}
        </View>
      )}

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <View style={styles.section}>
          <SectionHeader
            title="Recommended for You"
            subtitle="Based on your viewing history"
            icon="heart"
            onSeeAll={() => {/* Navigate to recommendations page */}}
          />
          {renderGridVideos(recommendations.slice(0, 6))}
        </View>
      )}

      {/* Recent Videos */}
      {recentVideos.length > 0 && (
        <View style={styles.section}>
          <SectionHeader
            title="Recently Added"
            subtitle="Fresh content uploaded today"
            icon="time"
            onSeeAll={() => {/* Navigate to recent page */}}
          />
          {renderGridVideos(recentVideos.slice(0, 6))}
        </View>
      )}

      {/* All Videos (if filtered) */}
      {(selectedCategory || searchQuery) && (
        <View style={styles.section}>
          <SectionHeader
            title={selectedCategory ? `${categories.find(c => c.id === selectedCategory)?.name} Videos` : 'Search Results'}
            subtitle={`${filteredVideos.length} videos found`}
          />
          {renderGridVideos(filteredVideos)}
        </View>
      )}

      {becauseYouWatched.length > 0 && (
        <View style={styles.section}>
          <SectionHeader
            title="Because you watched…"
            subtitle="Videos similar to your recent views"
            icon="play-circle"
          />
          {renderGridVideos(becauseYouWatched)}
        </View>
      )}
      {fromCreatorsYouFollow.length > 0 && (
        <View style={styles.section}>
          <SectionHeader
            title="New from creators you follow"
            subtitle="Fresh uploads from your favorites"
            icon="person-add"
          />
          {renderGridVideos(fromCreatorsYouFollow)}
        </View>
      )}
      {forYou.length > 0 && (
        <View style={styles.section}>
          <SectionHeader
            title="For You"
            subtitle="Handpicked just for you"
            icon="star"
          />
          {renderGridVideos(forYou)}
        </View>
      )}

      {/* Dynamic category sections */}
      {hotCategories.length > 0 && (
        <View style={styles.section}>
          <SectionHeader title="Hot Categories" icon="flame" />
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.featuredScroll}>
            {hotCategories.map((cat) => (
              <View key={cat.id} style={{ backgroundColor: GoGoColors.error, borderRadius: 12, padding: 12, marginRight: 12 }}>
                <Text style={{ color: '#fff', fontWeight: 'bold' }}>{cat.name}</Text>
                <Text style={{ color: '#fff', fontSize: 12 }}>Hot</Text>
              </View>
            ))}
          </ScrollView>
        </View>
      )}
      {newCategories.length > 0 && (
        <View style={styles.section}>
          <SectionHeader title="New Categories" icon="sparkles" />
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.featuredScroll}>
            {newCategories.map((cat) => (
              <View key={cat.id} style={{ backgroundColor: GoGoColors.primary, borderRadius: 12, padding: 12, marginRight: 12 }}>
                <Text style={{ color: '#fff', fontWeight: 'bold' }}>{cat.name}</Text>
                <Text style={{ color: '#fff', fontSize: 12 }}>New</Text>
              </View>
            ))}
          </ScrollView>
        </View>
      )}
      {risingCategories.length > 0 && (
        <View style={styles.section}>
          <SectionHeader title="Rising Categories" icon="trending-up" />
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.featuredScroll}>
            {risingCategories.map((cat) => (
              <View key={cat.id} style={{ backgroundColor: GoGoColors.highlightGold, borderRadius: 12, padding: 12, marginRight: 12 }}>
                <Text style={{ color: '#fff', fontWeight: 'bold' }}>{cat.name}</Text>
                <Text style={{ color: '#fff', fontSize: 12 }}>Rising</Text>
              </View>
            ))}
          </ScrollView>
        </View>
      )}

      <View style={styles.bottomSpacing} />

      {/* Database Test Modal - Temporarily simplified */}
      <Modal
        visible={showDatabaseTest}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
          <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>Database Test</Text>
          <Text style={{ fontSize: 16, textAlign: 'center', marginBottom: 20 }}>
            Your Supabase and Firebase are configured! 🎉{'\n\n'}
            Check your .env file for the credentials.
          </Text>
          <TouchableOpacity
            style={{ backgroundColor: '#2196F3', padding: 15, borderRadius: 10 }}
            onPress={() => setShowDatabaseTest(false)}
          >
            <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>Close</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </ScrollView>
  );

}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingText: {
    color: GoGoColors.textSecondary,
    fontSize: 16,
    marginTop: 16,
  },
  // Header Styles
  header: {
    marginBottom: 8,
  },
  headerGradient: {
    paddingTop: 25,
    paddingBottom: 8,
  },
  headerContent: {
    paddingHorizontal: 20,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  welcomeText: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    fontWeight: '500',
  },
  notificationButton: {
    position: 'relative',
    padding: 8,
  },
  notificationBadge: {
    position: 'absolute',
    top: 6,
    right: 6,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: GoGoColors.error,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 0,
  },
  // Category Filter Styles
  categoryFilter: {
    marginBottom: 12,
  },
  categoryScroll: {
    paddingHorizontal: 20,
    gap: 12,
  },
  categoryChip: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
    backgroundColor: GoGoColors.backgroundLight,
    borderWidth: 1,
    borderColor: GoGoColors.border,
  },
  categoryChipActive: {
    backgroundColor: GoGoColors.primary,
    borderColor: GoGoColors.primary,
  },
  categoryChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: GoGoColors.textSecondary,
  },
  categoryChipTextActive: {
    color: '#FFFFFF',
  },
  // Section Styles
  section: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sectionTextContainer: {
    marginLeft: 12,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: GoGoColors.backgroundLight,
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: GoGoColors.primary,
    marginRight: 4,
  },
  // Featured Section Styles
  featuredSection: {
    marginBottom: 20,
  },
  featuredScroll: {
    paddingHorizontal: 20,
    gap: 16,
  },
  featuredCard: {
    width: width * 0.85,
    height: width * 0.6,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: GoGoColors.backgroundCard,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  featuredImage: {
    width: '100%',
    height: '100%',
  },
  featuredGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
    justifyContent: 'flex-end',
  },
  featuredContent: {
    padding: 20,
  },
  featuredBadges: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 8,
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.premium,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  premiumText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  categoryBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  featuredTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  featuredCreator: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8,
  },
  featuredStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  // Grid and Card Styles
  gridContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  modernCard: {
    flex: 1,
    marginHorizontal: 8,
    marginBottom: 20,
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  cardTouchable: {
    flex: 1,
  },
  cardImageContainer: {
    position: 'relative',
    aspectRatio: 16 / 9,
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  cardPremiumBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: GoGoColors.premium,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  playOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
    opacity: 0.8,
  },
  cardContent: {
    padding: 12,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
    lineHeight: 20,
  },
  cardCreator: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    marginBottom: 8,
  },
  cardStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardStatText: {
    fontSize: 12,
    color: GoGoColors.textMuted,
  },
  cardDuration: {
    fontSize: 12,
    color: GoGoColors.textMuted,
    backgroundColor: GoGoColors.backgroundLight,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  bottomSpacing: {
    height: 100,
  },
});
