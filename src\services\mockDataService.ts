import { Video, Category, User } from '../types';

// Mock creators
const mockCreators: User[] = [
  {
    id: 'creator-1',
    email: '<EMAIL>',
    username: '<PERSON><PERSON><PERSON><PERSON>',
    full_name: '<PERSON>',
    avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    role: 'creator',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'creator-2',
    email: '<EMAIL>',
    username: '<PERSON><PERSON><PERSON>',
    full_name: '<PERSON>',
    avatar_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    role: 'creator',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'creator-3',
    email: '<EMAIL>',
    username: '<PERSON><PERSON><PERSON><PERSON>',
    full_name: '<PERSON>',
    avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    role: 'creator',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'creator-4',
    email: '<EMAIL>',
    username: 'MercyKamanga',
    full_name: 'Mercy Kamanga',
    avatar_url: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    role: 'creator',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'creator-5',
    email: '<EMAIL>',
    username: 'FrankChimwemwe',
    full_name: 'Frank Chimwemwe',
    avatar_url: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    role: 'creator',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

// Mock categories
const mockCategories: Category[] = [
  {
    id: 'cat-1',
    name: 'Comedy',
    description: 'Funny videos and skits',
    thumbnail_url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=300&h=200&fit=crop',
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'cat-2',
    name: 'Music',
    description: 'Music videos and performances',
    thumbnail_url: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=200&fit=crop',
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'cat-3',
    name: 'Education',
    description: 'Educational and tutorial content',
    thumbnail_url: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=200&fit=crop',
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'cat-4',
    name: 'Sports',
    description: 'Sports highlights and analysis',
    thumbnail_url: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=300&h=200&fit=crop',
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'cat-5',
    name: 'News',
    description: 'Local and international news',
    thumbnail_url: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=300&h=200&fit=crop',
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'cat-6',
    name: 'Lifestyle',
    description: 'Lifestyle and fashion content',
    thumbnail_url: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=300&h=200&fit=crop',
    created_at: '2024-01-01T00:00:00Z',
  },
];

// Mock videos
const mockVideos: Video[] = [
  {
    id: 'video-1',
    title: 'Malawian Comedy: Village Life',
    description: 'A hilarious take on everyday village life in Malawi. Join us for laughs and cultural insights!',
    thumbnail_url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    duration: 596,
    creator_id: 'creator-1',
    creator: mockCreators[0],
    category_id: 'cat-1',
    category: mockCategories[0],
    price: 300,
    is_premium: false,
    view_count: 15420,
    like_count: 1240,
    created_at: '2024-01-10T10:30:00Z',
    updated_at: '2024-01-10T10:30:00Z',
    tags: ['comedy', 'village', 'malawi', 'culture'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4', file_size: 50000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4', file_size: 80000000 },
    ],
  },
  {
    id: 'video-2',
    title: 'Afrobeat Fusion Live Performance',
    description: 'Experience the best of Malawian Afrobeat fusion in this electrifying live performance.',
    thumbnail_url: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    duration: 654,
    creator_id: 'creator-2',
    creator: mockCreators[1],
    category_id: 'cat-2',
    category: mockCategories[1],
    price: 300,
    is_premium: true,
    view_count: 8930,
    like_count: 890,
    created_at: '2024-01-12T14:20:00Z',
    updated_at: '2024-01-12T14:20:00Z',
    tags: ['music', 'afrobeat', 'live', 'performance'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4', file_size: 60000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4', file_size: 95000000 },
    ],
  },
  {
    id: 'video-3',
    title: 'Learn Chichewa: Basic Greetings',
    description: 'Master the basics of Chichewa with this comprehensive tutorial on common greetings and phrases.',
    thumbnail_url: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    duration: 420,
    creator_id: 'creator-3',
    creator: mockCreators[2],
    category_id: 'cat-3',
    category: mockCategories[2],
    price: 0,
    is_premium: false,
    view_count: 12350,
    like_count: 1150,
    created_at: '2024-01-08T09:15:00Z',
    updated_at: '2024-01-08T09:15:00Z',
    tags: ['education', 'chichewa', 'language', 'tutorial'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4', file_size: 40000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4', file_size: 65000000 },
    ],
  },
  {
    id: 'video-4',
    title: 'Football Highlights: Bullets vs Wanderers',
    description: 'Catch all the action from the epic match between Nyasa Big Bullets and Mighty Wanderers.',
    thumbnail_url: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=400&h=300&fit=crop',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
    duration: 780,
    creator_id: 'creator-4',
    creator: mockCreators[3],
    category_id: 'cat-4',
    category: mockCategories[3],
    price: 300,
    is_premium: false,
    view_count: 25670,
    like_count: 2340,
    created_at: '2024-01-14T16:45:00Z',
    updated_at: '2024-01-14T16:45:00Z',
    tags: ['sports', 'football', 'bullets', 'wanderers'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4', file_size: 70000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4', file_size: 110000000 },
    ],
  },
  {
    id: 'video-5',
    title: 'Breaking: Economic Update from Lilongwe',
    description: 'Latest economic developments and their impact on everyday Malawians.',
    thumbnail_url: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=400&h=300&fit=crop',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
    duration: 360,
    creator_id: 'creator-5',
    creator: mockCreators[4],
    category_id: 'cat-5',
    category: mockCategories[4],
    price: 0,
    is_premium: false,
    view_count: 18920,
    like_count: 1680,
    created_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-15T08:00:00Z',
    tags: ['news', 'economy', 'lilongwe', 'update'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4', file_size: 35000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4', file_size: 55000000 },
    ],
  },
  {
    id: 'video-6',
    title: 'Traditional Malawian Cooking: Nsima',
    description: 'Learn how to prepare perfect nsima with traditional techniques passed down through generations.',
    thumbnail_url: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4',
    duration: 480,
    creator_id: 'creator-2',
    creator: mockCreators[1],
    category_id: 'cat-6',
    category: mockCategories[5],
    price: 300,
    is_premium: false,
    view_count: 9870,
    like_count: 890,
    created_at: '2024-01-13T11:30:00Z',
    updated_at: '2024-01-13T11:30:00Z',
    tags: ['lifestyle', 'cooking', 'nsima', 'traditional'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4', file_size: 45000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4', file_size: 70000000 },
    ],
  },
  {
    id: 'video-7',
    title: 'Stand-up Comedy: City vs Village',
    description: 'Hilarious observations about the differences between city and village life in Malawi.',
    thumbnail_url: 'https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=400&h=300&fit=crop',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4',
    duration: 720,
    creator_id: 'creator-1',
    creator: mockCreators[0],
    category_id: 'cat-1',
    category: mockCategories[0],
    price: 300,
    is_premium: true,
    view_count: 22340,
    like_count: 2100,
    created_at: '2024-01-11T19:00:00Z',
    updated_at: '2024-01-11T19:00:00Z',
    tags: ['comedy', 'standup', 'city', 'village'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4', file_size: 65000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4', file_size: 100000000 },
    ],
  },
  {
    id: 'video-8',
    title: 'Guitar Tutorial: Malawian Folk Songs',
    description: 'Learn to play traditional Malawian folk songs on guitar with step-by-step instructions.',
    thumbnail_url: 'https://images.unsplash.com/photo-1510915361894-db8b60106cb1?w=400&h=300&fit=crop',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
    duration: 540,
    creator_id: 'creator-3',
    creator: mockCreators[2],
    category_id: 'cat-2',
    category: mockCategories[1],
    price: 0,
    is_premium: false,
    view_count: 7650,
    like_count: 720,
    created_at: '2024-01-09T15:45:00Z',
    updated_at: '2024-01-09T15:45:00Z',
    tags: ['music', 'guitar', 'tutorial', 'folk'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4', file_size: 50000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4', file_size: 80000000 },
    ],
  },
  {
    id: 'video-9',
    title: 'Basketball Skills: Dribbling Masterclass',
    description: 'Improve your basketball skills with advanced dribbling techniques and drills.',
    thumbnail_url: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=300&fit=crop',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4',
    duration: 600,
    creator_id: 'creator-4',
    creator: mockCreators[3],
    category_id: 'cat-4',
    category: mockCategories[3],
    price: 300,
    is_premium: false,
    view_count: 11230,
    like_count: 980,
    created_at: '2024-01-07T13:20:00Z',
    updated_at: '2024-01-07T13:20:00Z',
    tags: ['sports', 'basketball', 'skills', 'tutorial'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4', file_size: 55000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4', file_size: 85000000 },
    ],
  },
  {
    id: 'video-10',
    title: 'Fashion Trends: Malawian Style 2024',
    description: 'Discover the latest fashion trends combining traditional Malawian style with modern aesthetics.',
    thumbnail_url: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=300&fit=crop',
    video_url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/VolkswagenGTIReview.mp4',
    duration: 450,
    creator_id: 'creator-5',
    creator: mockCreators[4],
    category_id: 'cat-6',
    category: mockCategories[5],
    price: 300,
    is_premium: true,
    view_count: 14560,
    like_count: 1320,
    created_at: '2024-01-06T12:00:00Z',
    updated_at: '2024-01-06T12:00:00Z',
    tags: ['lifestyle', 'fashion', 'trends', 'style'],
    quality_options: [
      { quality: '480p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/VolkswagenGTIReview.mp4', file_size: 42000000 },
      { quality: '720p', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/VolkswagenGTIReview.mp4', file_size: 68000000 },
    ],
  },
];

export const mockDataService = {
  getVideos: () => {
    console.log('mockDataService.getVideos called, returning:', mockVideos.length, 'videos');
    return mockVideos;
  },
  getCategories: () => {
    console.log('mockDataService.getCategories called, returning:', mockCategories.length, 'categories');
    return mockCategories;
  },
  getCreators: () => mockCreators,
  getVideoById: (id: string) => mockVideos.find(v => v.id === id),
  getCategoryById: (id: string) => mockCategories.find(c => c.id === id),
  getCreatorById: (id: string) => mockCreators.find(c => c.id === id),
  getVideosByCategory: (categoryId: string) => mockVideos.filter(v => v.category_id === categoryId),
  getVideosByCreator: (creatorId: string) => mockVideos.filter(v => v.creator_id === creatorId),
  getFeaturedVideos: () => mockVideos.filter(v => v.view_count > 10000),
  getTrendingVideos: () => mockVideos.sort((a, b) => b.view_count - a.view_count).slice(0, 6),
  getRecentVideos: () => mockVideos.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()).slice(0, 6),
  getFreeVideos: () => mockVideos.filter(v => v.price === 0),
  getPremiumVideos: () => mockVideos.filter(v => v.is_premium),
};
