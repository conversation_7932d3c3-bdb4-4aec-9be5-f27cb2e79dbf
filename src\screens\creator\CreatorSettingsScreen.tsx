import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  TextInput,
  Modal,
  Dimensions,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  SlideInDown,
  SlideInRight,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import { updateProfile } from '../../store/slices/authSlice';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';


const { width, height } = Dimensions.get('window');

interface Props {
  onClose: () => void;
}

interface SettingItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  value?: string | boolean;
  onPress?: () => void;
  type?: 'toggle' | 'input' | 'select' | 'action' | 'info' | 'badge';
  index: number;
  disabled?: boolean;
  color?: string;
}

function SettingItem({
  icon,
  title,
  subtitle,
  value,
  onPress,
  type = 'action',
  index,
  disabled = false,
  color = GoGoColors.primary
}: SettingItemProps) {

  return (
    <Animated.View entering={SlideInRight.delay(index * 50)}>
      <TouchableOpacity
        style={[styles.modernSettingItem, disabled && styles.settingItemDisabled]}
        onPress={onPress}
        disabled={disabled || type === 'toggle'}
        activeOpacity={0.7}
      >
        <View style={styles.settingContent}>
          <View style={styles.settingLeft}>
            <View style={[styles.modernIconContainer, { backgroundColor: color }]}>
              <Ionicons
                name={icon as any}
                size={20}
                color="#FFFFFF"
              />
            </View>
            <View style={styles.settingText}>
              <Text style={[styles.modernSettingTitle, disabled && styles.settingTitleDisabled]}>{title}</Text>
              {subtitle && <Text style={[styles.modernSettingSubtitle, disabled && styles.settingSubtitleDisabled]}>{subtitle}</Text>}
            </View>
          </View>

          <View style={styles.settingRight}>
            {type === 'toggle' && typeof value === 'boolean' && (
              <Switch
                value={value}
                onValueChange={onPress}
                trackColor={{ false: '#E5E7EB', true: color }}
                thumbColor="#FFFFFF"
                disabled={disabled}
              />
            )}
            {type === 'input' && typeof value === 'string' && (
              <View style={styles.modernValueContainer}>
                <Text style={[styles.modernValueText, disabled && styles.valueTextDisabled]}>{value}</Text>
                <Ionicons name="chevron-forward" size={16} color="#9CA3AF" />
              </View>
            )}
            {type === 'select' && (
              <View style={styles.modernValueContainer}>
                <Text style={[styles.modernValueText, disabled && styles.valueTextDisabled]}>{value}</Text>
                <Ionicons name="chevron-forward" size={16} color="#9CA3AF" />
              </View>
            )}
            {type === 'action' && (
              <Ionicons name="chevron-forward" size={16} color="#9CA3AF" />
            )}
            {type === 'info' && typeof value === 'string' && (
              <Text style={[styles.modernValueText, disabled && styles.valueTextDisabled]}>{value}</Text>
            )}
            {type === 'badge' && typeof value === 'string' && (
              <View style={[styles.modernBadge, { backgroundColor: color + '15' }]}>
                <Text style={[styles.modernBadgeText, { color: color }]}>{value}</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

export default function CreatorSettingsScreen({ onClose }: Props) {
  const dispatch = useAppDispatch();
  const { user, isLoading } = useAppSelector((state) => state.auth);

  // Profile Settings
  const [displayName, setDisplayName] = useState(user?.full_name || '');
  const [bio, setBio] = useState('Professional content creator passionate about sharing knowledge and entertainment.');
  const [website, setWebsite] = useState('');
  const [socialLinks, setSocialLinks] = useState({
    youtube: '',
    instagram: '',
    twitter: '',
    tiktok: '',
  });

  // Monetization Settings
  const [monthlyPrice, setMonthlyPrice] = useState('1500');
  const [ppvEnabled, setPpvEnabled] = useState(true);
  const [defaultPpvPrice, setDefaultPpvPrice] = useState('300');
  const [subscriptionEnabled, setSubscriptionEnabled] = useState(true);
  const [revenueShare, setRevenueShare] = useState('80'); // Creator gets 80%
  const [autoPriceIncrease, setAutoPriceIncrease] = useState(false);
  
  // Content Settings
  const [autoPublish, setAutoPublish] = useState(false);
  const [contentRating, setContentRating] = useState('General');
  const [allowComments, setAllowComments] = useState(true);
  const [allowDownloads, setAllowDownloads] = useState(false);
  const [watermarkEnabled, setWatermarkEnabled] = useState(true);
  const [defaultVisibility, setDefaultVisibility] = useState('Public');
  
  // Notification Settings
  const [newSubscriberNotif, setNewSubscriberNotif] = useState(true);
  const [purchaseNotif, setPurchaseNotif] = useState(true);
  const [commentNotif, setCommentNotif] = useState(true);
  const [earningsNotif, setEarningsNotif] = useState(true);
  const [weeklyReport, setWeeklyReport] = useState(true);
  const [marketingEmails, setMarketingEmails] = useState(false);
  
  // Payment Settings
  const [paymentMethod, setPaymentMethod] = useState('Airtel Money');
  const [phoneNumber, setPhoneNumber] = useState('+*********** 456');
  const [autoWithdraw, setAutoWithdraw] = useState(false);
  const [withdrawalThreshold, setWithdrawalThreshold] = useState('10000');
  const [taxInfo, setTaxInfo] = useState('Not provided');
  
  // Privacy Settings
  const [profileVisibility, setProfileVisibility] = useState('Public');
  const [showEarnings, setShowEarnings] = useState(false);
  const [allowMessages, setAllowMessages] = useState(true);
  const [blockedUsers, setBlockedUsers] = useState('0 users');
  
  // Modal States
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showPriceModal, setShowPriceModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [showSocialModal, setShowSocialModal] = useState(false);
  const [currentEditField, setCurrentEditField] = useState('');
  const [tempValue, setTempValue] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (user) {
      setDisplayName(user.full_name);
    }
  }, [user]);

  const handleProfileEdit = (field: string, currentValue: string) => {
    hapticFeedback.light();
    setCurrentEditField(field);
    setTempValue(currentValue);
    setShowProfileModal(true);
  };

  const handlePriceEdit = (field: string, currentValue: string) => {
    hapticFeedback.light();
    setCurrentEditField(field);
    setTempValue(currentValue);
    setShowPriceModal(true);
  };

  const handleSaveProfile = () => {
    if (tempValue.trim()) {
      switch (currentEditField) {
        case 'displayName':
          setDisplayName(tempValue);
          break;
        case 'bio':
          setBio(tempValue);
          break;
        case 'website':
          setWebsite(tempValue);
          break;
      }
      setShowProfileModal(false);
      hapticFeedback.success();
    } else {
      Alert.alert('Error', 'This field cannot be empty');
    }
  };

  const handleSavePrice = () => {
    if (tempValue && !isNaN(Number(tempValue))) {
      switch (currentEditField) {
        case 'monthly':
          setMonthlyPrice(tempValue);
          break;
        case 'ppv':
          setDefaultPpvPrice(tempValue);
          break;
        case 'threshold':
          setWithdrawalThreshold(tempValue);
          break;
      }
      setShowPriceModal(false);
      hapticFeedback.success();
    } else {
      Alert.alert('Error', 'Please enter a valid amount');
    }
  };

  const handlePaymentMethodChange = () => {
    hapticFeedback.light();
    setShowPaymentModal(true);
  };

  const handleContentRatingChange = () => {
    hapticFeedback.light();
    setShowRatingModal(true);
  };

  const handleSocialLinksEdit = () => {
    hapticFeedback.light();
    setShowSocialModal(true);
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    hapticFeedback.light();

    try {
      // Update user profile
      if (user) {
        await dispatch(updateProfile({
          full_name: displayName,
          // Add other profile fields as needed
        })).unwrap();
      }

      // Simulate API call for settings
      await new Promise(resolve => setTimeout(resolve, 1500));

      hapticFeedback.success();
      Alert.alert(
        'Settings Saved',
        'Your creator settings have been updated successfully!',
        [{ text: 'OK', onPress: onClose }]
      );
    } catch (error) {
      hapticFeedback.error();
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            hapticFeedback.error();
            Alert.alert('Account Deleted', 'Your account has been deleted successfully.');
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Modern Header */}
      <Animated.View style={styles.modernHeader} entering={FadeIn}>
        <View style={styles.headerContent}>
          <TouchableOpacity style={styles.backButton} onPress={onClose}>
            <Ionicons name="arrow-back" size={24} color="#1F2937" />
          </TouchableOpacity>

          <View style={styles.headerCenter}>
            <Text style={styles.modernHeaderTitle}>Settings</Text>
          </View>

          <TouchableOpacity
            style={[styles.modernSaveButton, isSaving && styles.saveButtonDisabled]}
            onPress={handleSaveSettings}
            disabled={isSaving}
          >
            {isSaving ? (
              <ActivityIndicator size="small" color="#1DA1F2" />
            ) : (
              <Text style={styles.modernSaveButtonText}>Save</Text>
            )}
          </TouchableOpacity>
        </View>
      </Animated.View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(200)}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Profile</Text>
            <Text style={styles.sectionDescription}>Manage your creator profile</Text>
          </View>

          <SettingItem
            icon="person-outline"
            title="Display Name"
            subtitle="Your public creator name"
            value={displayName}
            onPress={() => handleProfileEdit('displayName', displayName)}
            type="input"
            index={0}
            color="#1DA1F2"
          />

          <SettingItem
            icon="document-text-outline"
            title="Bio"
            subtitle="Tell your audience about yourself"
            value={bio.length > 50 ? bio.substring(0, 50) + '...' : bio || 'Not set'}
            onPress={() => handleProfileEdit('bio', bio)}
            type="input"
            index={1}
            color="#1DA1F2"
          />

          <SettingItem
            icon="globe-outline"
            title="Website"
            subtitle="Your personal website"
            value={website || 'Not set'}
            onPress={() => handleProfileEdit('website', website)}
            type="input"
            index={2}
            color="#1DA1F2"
          />

          <SettingItem
            icon="share-social-outline"
            title="Social Links"
            subtitle="Connect your social media accounts"
            value="Manage"
            onPress={handleSocialLinksEdit}
            type="action"
            index={3}
            color="#1DA1F2"
          />
        </Animated.View>

        {/* Monetization Section */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(300)}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Monetization</Text>
            <Text style={styles.sectionDescription}>Configure your revenue streams</Text>
          </View>

          <SettingItem
            icon="card-outline"
            title="Monthly Subscription"
            subtitle={subscriptionEnabled ? 'Enabled' : 'Disabled'}
            value={subscriptionEnabled}
            onPress={() => setSubscriptionEnabled(!subscriptionEnabled)}
            type="toggle"
            index={4}
            color="#10B981"
          />

          {subscriptionEnabled && (
            <SettingItem
              icon="cash-outline"
              title="Monthly Price"
              subtitle="Set your subscription price"
              value={`$${monthlyPrice}`}
              onPress={() => handlePriceEdit('monthly', monthlyPrice)}
              type="input"
              index={5}
              color="#10B981"
            />
          )}

          <SettingItem
            icon="pricetag-outline"
            title="Pay-Per-View"
            subtitle={ppvEnabled ? 'Enabled' : 'Disabled'}
            value={ppvEnabled}
            onPress={() => setPpvEnabled(!ppvEnabled)}
            type="toggle"
            index={6}
            color="#10B981"
          />
          
          {ppvEnabled && (
            <SettingItem
              icon="cash-outline"
              title="Default PPV Price"
              subtitle="Default price for individual videos"
              value={`$${defaultPpvPrice}`}
              onPress={() => handlePriceEdit('ppv', defaultPpvPrice)}
              type="input"
              index={7}
            />
          )}
          
          <SettingItem
            icon="trending-up-outline"
            title="Auto Price Increase"
            subtitle="Automatically increase prices based on demand"
            value={autoPriceIncrease}
            onPress={() => setAutoPriceIncrease(!autoPriceIncrease)}
            type="toggle"
            index={8}
          />
          
                      <SettingItem
              icon="pie-chart-outline"
              title="Revenue Share"
              subtitle="Your share of revenue"
              value={`${revenueShare}%`}
              type="badge"
              index={9}
              color={GoGoColors.highlightGold}
            />
        </Animated.View>

        {/* Content Settings */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(400)}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Content</Text>
            <Text style={styles.sectionDescription}>Control your video settings</Text>
          </View>
          
          <SettingItem
            icon="cloud-upload-outline"
            title="Auto Publish"
            subtitle="Automatically publish uploaded videos"
            value={autoPublish}
            onPress={() => setAutoPublish(!autoPublish)}
            type="toggle"
            index={10}
          />
          
                      <SettingItem
              icon="shield-checkmark-outline"
              title="Content Rating"
              subtitle="Set default content rating"
              value={contentRating}
              onPress={handleContentRatingChange}
              type="select"
              index={11}
              color={GoGoColors.success}
            />
          
          <SettingItem
            icon="chatbubbles-outline"
            title="Allow Comments"
            subtitle="Let viewers comment on your videos"
            value={allowComments}
            onPress={() => setAllowComments(!allowComments)}
            type="toggle"
            index={12}
          />
          
          <SettingItem
            icon="download-outline"
            title="Allow Downloads"
            subtitle="Let viewers download your videos"
            value={allowDownloads}
            onPress={() => setAllowDownloads(!allowDownloads)}
            type="toggle"
            index={13}
          />
          
          <SettingItem
            icon="water-outline"
            title="Watermark Videos"
            subtitle="Add your logo to videos"
            value={watermarkEnabled}
            onPress={() => setWatermarkEnabled(!watermarkEnabled)}
            type="toggle"
            index={14}
          />
          
          <SettingItem
            icon="eye-outline"
            title="Default Visibility"
            subtitle="Set default video visibility"
            value={defaultVisibility}
            type="select"
            index={15}
          />
        </Animated.View>

        {/* Notifications */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(500)}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Notifications</Text>
            <Text style={styles.sectionDescription}>Manage your alerts</Text>
          </View>
          
                      <SettingItem
              icon="person-add-outline"
              title="New Subscribers"
              subtitle="Get notified when someone subscribes"
              value={newSubscriberNotif}
              onPress={() => setNewSubscriberNotif(!newSubscriberNotif)}
              type="toggle"
              index={16}
              color={GoGoColors.warning}
            />
          
          <SettingItem
            icon="card-outline"
            title="Purchases"
            subtitle="Get notified of video purchases"
            value={purchaseNotif}
            onPress={() => setPurchaseNotif(!purchaseNotif)}
            type="toggle"
            index={17}
          />
          
          <SettingItem
            icon="chatbubble-outline"
            title="Comments"
            subtitle="Get notified of new comments"
            value={commentNotif}
            onPress={() => setCommentNotif(!commentNotif)}
            type="toggle"
            index={18}
          />
          
          <SettingItem
            icon="trending-up-outline"
            title="Earnings Updates"
            subtitle="Get notified of earnings milestones"
            value={earningsNotif}
            onPress={() => setEarningsNotif(!earningsNotif)}
            type="toggle"
            index={19}
          />
          
          <SettingItem
            icon="bar-chart-outline"
            title="Weekly Reports"
            subtitle="Receive weekly performance reports"
            value={weeklyReport}
            onPress={() => setWeeklyReport(!weeklyReport)}
            type="toggle"
            index={20}
          />
          
          <SettingItem
            icon="mail-outline"
            title="Marketing Emails"
            subtitle="Receive promotional emails"
            value={marketingEmails}
            onPress={() => setMarketingEmails(!marketingEmails)}
            type="toggle"
            index={21}
          />
        </Animated.View>

        {/* Payment Settings */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(600)}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Payment</Text>
            <Text style={styles.sectionDescription}>Configure your payment methods</Text>
          </View>
          
          <SettingItem
            icon="card-outline"
            title="Payment Method"
            subtitle="How you receive payments"
            value={paymentMethod}
            onPress={handlePaymentMethodChange}
            type="select"
            index={22}
          />
          
          <SettingItem
            icon="call-outline"
            title="Phone Number"
            subtitle="For payment verification"
            value={phoneNumber}
            type="info"
            index={23}
          />
          
          <SettingItem
            icon="sync-outline"
            title="Auto Withdraw"
            subtitle="Automatically withdraw earnings"
            value={autoWithdraw}
            onPress={() => setAutoWithdraw(!autoWithdraw)}
            type="toggle"
            index={24}
          />
          
          {autoWithdraw && (
            <SettingItem
              icon="cash-outline"
              title="Withdrawal Threshold"
              subtitle="Minimum amount for auto-withdrawal"
              value={`$${withdrawalThreshold}`}
              onPress={() => handlePriceEdit('threshold', withdrawalThreshold)}
              type="input"
              index={25}
            />
          )}
          
          <SettingItem
            icon="document-text-outline"
            title="Tax Information"
            subtitle="Your tax details for payments"
            value={taxInfo}
            type="action"
            index={26}
          />
        </Animated.View>

        {/* Privacy Settings */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(700)}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Privacy</Text>
            <Text style={styles.sectionDescription}>Control your privacy settings</Text>
          </View>
          
          <SettingItem
            icon="eye-outline"
            title="Profile Visibility"
            subtitle="Who can see your profile"
            value={profileVisibility}
            type="select"
            index={27}
          />
          
          <SettingItem
            icon="trending-up-outline"
            title="Show Earnings"
            subtitle="Display earnings on profile"
            value={showEarnings}
            onPress={() => setShowEarnings(!showEarnings)}
            type="toggle"
            index={28}
          />
          
          <SettingItem
            icon="mail-outline"
            title="Allow Messages"
            subtitle="Let viewers send you messages"
            value={allowMessages}
            onPress={() => setAllowMessages(!allowMessages)}
            type="toggle"
            index={29}
          />
          
          <SettingItem
            icon="ban-outline"
            title="Blocked Users"
            subtitle="Manage blocked users"
            value={blockedUsers}
            type="action"
            index={30}
          />
        </Animated.View>

        {/* Danger Zone */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(800)}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Danger Zone</Text>
            <Text style={styles.sectionDescription}>Irreversible actions</Text>
          </View>
          
          <SettingItem
            icon="trash-outline"
            title="Delete Account"
            subtitle="Permanently delete your account and all content"
            value=""
            onPress={handleDeleteAccount}
            type="action"
            index={31}
          />
        </Animated.View>
      </ScrollView>

      {/* Profile Edit Modal */}
      <Modal
        visible={showProfileModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowProfileModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              {currentEditField === 'displayName' ? 'Edit Display Name' :
               currentEditField === 'bio' ? 'Edit Bio' : 'Edit Website'}
            </Text>
            <TextInput
              style={[
                styles.modalInput,
                currentEditField === 'bio' && styles.modalInputMultiline
              ]}
              value={tempValue}
              onChangeText={setTempValue}
              placeholder={
                currentEditField === 'displayName' ? 'Enter display name' :
                currentEditField === 'bio' ? 'Tell your audience about yourself...' :
                'Enter website URL'
              }
              multiline={currentEditField === 'bio'}
              numberOfLines={currentEditField === 'bio' ? 4 : 1}
              textAlignVertical={currentEditField === 'bio' ? 'top' : 'center'}
              maxLength={currentEditField === 'bio' ? 500 : 100}
            />
            {currentEditField === 'bio' && (
              <Text style={styles.characterCounter}>
                {tempValue.length}/500 characters
              </Text>
            )}
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowProfileModal(false)}
              >
                <Text style={styles.modalButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonPrimary]}
                onPress={handleSaveProfile}
              >
                <Text style={[styles.modalButtonText, styles.modalButtonTextPrimary]}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Price Edit Modal */}
      <Modal
        visible={showPriceModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPriceModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              {currentEditField === 'monthly' ? 'Edit Monthly Price' :
               currentEditField === 'ppv' ? 'Edit PPV Price' : 'Edit Withdrawal Threshold'}
            </Text>
            <TextInput
              style={styles.modalInput}
              value={tempValue}
              onChangeText={setTempValue}
              placeholder="Enter amount"
              keyboardType="numeric"
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowPriceModal(false)}
              >
                <Text style={styles.modalButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonPrimary]}
                onPress={handleSavePrice}
              >
                <Text style={[styles.modalButtonText, styles.modalButtonTextPrimary]}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Payment Method Modal */}
      <Modal
        visible={showPaymentModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPaymentModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Payment Method</Text>
            {['Airtel Money', 'MPesa', 'Bank Transfer', 'PayPal'].map((method) => (
              <TouchableOpacity
                key={method}
                style={[styles.paymentOption, paymentMethod === method && styles.paymentOptionSelected]}
                onPress={() => {
                  setPaymentMethod(method);
                  setShowPaymentModal(false);
                  hapticFeedback.success();
                }}
              >
                <Text style={[styles.paymentOptionText, paymentMethod === method && styles.paymentOptionTextSelected]}>
                  {method}
                </Text>
                {paymentMethod === method && (
                  <Ionicons name="checkmark" size={20} color={GoGoColors.primary} />
                )}
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => setShowPaymentModal(false)}
            >
              <Text style={styles.modalButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Content Rating Modal */}
      <Modal
        visible={showRatingModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowRatingModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Content Rating</Text>
            {['General', 'Teen', 'Mature', 'Adult'].map((rating) => (
              <TouchableOpacity
                key={rating}
                style={[styles.paymentOption, contentRating === rating && styles.paymentOptionSelected]}
                onPress={() => {
                  setContentRating(rating);
                  setShowRatingModal(false);
                  hapticFeedback.success();
                }}
              >
                <Text style={[styles.paymentOptionText, contentRating === rating && styles.paymentOptionTextSelected]}>
                  {rating}
                </Text>
                {contentRating === rating && (
                  <Ionicons name="checkmark" size={20} color={GoGoColors.primary} />
                )}
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => setShowRatingModal(false)}
            >
              <Text style={styles.modalButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Social Links Modal */}
      <Modal
        visible={showSocialModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSocialModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Social Media Links</Text>
            <Text style={styles.modalSubtitle}>Connect your social media accounts</Text>
            
            {Object.entries(socialLinks).map(([platform, url]) => (
              <View key={platform} style={styles.socialInputContainer}>
                <Text style={styles.socialLabel}>{platform.charAt(0).toUpperCase() + platform.slice(1)}</Text>
                <TextInput
                  style={styles.socialInput}
                  value={url}
                  onChangeText={(text) => setSocialLinks(prev => ({ ...prev, [platform]: text }))}
                  placeholder={`Enter your ${platform} URL`}
                />
              </View>
            ))}
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowSocialModal(false)}
              >
                <Text style={styles.modalButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonPrimary]}
                onPress={() => {
                  setShowSocialModal(false);
                  hapticFeedback.success();
                }}
              >
                <Text style={[styles.modalButtonText, styles.modalButtonTextPrimary]}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  modernHeader: {
    backgroundColor: '#FFFFFF',
    paddingTop: 44,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  modernHeaderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  modernSaveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#1DA1F2',
  },
  modernSaveButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 24,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 32,
  },
  sectionHeader: {
    marginBottom: 16,
    paddingHorizontal: 4,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4,
    lineHeight: 24,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  modernSettingItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  settingContent: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 60,
  },
  settingItemDisabled: {
    opacity: 0.5,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    paddingRight: 12,
  },
  modernIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    flexShrink: 0,
  },
  settingText: {
    flex: 1,
    justifyContent: 'center',
  },
  modernSettingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
    lineHeight: 20,
  },
  modernSettingSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  settingTitleDisabled: {
    color: '#9CA3AF',
  },
  settingSubtitleDisabled: {
    color: '#9CA3AF',
  },
  settingRight: {
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
  },
  modernValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingLeft: 8,
  },
  modernValueText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
    textAlign: 'right',
    maxWidth: 120,
    numberOfLines: 1,
    ellipsizeMode: 'tail',
  },
  valueTextDisabled: {
    color: '#9CA3AF',
  },
  modernBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  modernBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  chevronContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  infoContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  infoText: {
    fontSize: 14,
    fontWeight: '600',
  },
  badgeContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '700',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
  },
  modalInputMultiline: {
    height: 120,
    paddingTop: 16,
    textAlignVertical: 'top',
  },
  characterCounter: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'right',
    marginTop: -16,
    marginBottom: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: GoGoColors.backgroundLight,
    alignItems: 'center',
  },
  modalButtonPrimary: {
    backgroundColor: GoGoColors.primary,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
  },
  modalButtonTextPrimary: {
    color: '#FFFFFF',
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: GoGoColors.backgroundLight,
    marginBottom: 8,
  },
  paymentOptionSelected: {
    backgroundColor: GoGoColors.primary + '15',
    borderColor: GoGoColors.primary,
    borderWidth: 1,
  },
  paymentOptionText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  paymentOptionTextSelected: {
    color: GoGoColors.primary,
    fontWeight: '600',
  },
  socialInputContainer: {
    marginBottom: 16,
  },
  socialLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
    marginBottom: 8,
  },
  socialInput: {
    borderWidth: 1,
    borderColor: GoGoColors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: GoGoColors.textPrimary,
    backgroundColor: GoGoColors.backgroundLight,
  },
}); 